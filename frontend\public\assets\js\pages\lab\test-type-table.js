/*
 * Test Type Table
 * Clean, simple table for displaying test type information
 * Uses new modular Table architecture
 */
import { Table } from '../../core/components/table.js';
import { formatStatusDisplay } from '../../core/helpers/format-helpers.js';
import { fetchTestTypesForLab } from './shared/lab-helpers.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    test: TEST_CONFIGS
};

class TestTypeTable extends Table {
    constructor(options = {}) {
        super({
            containerId: options.containerId || TEST_CONFIGS.ui.testTypeTable.containerId,
            tableId: options.tableId || TEST_CONFIGS.ui.testTypeTable.tableId,
            columns: TEST_CONFIGS.ui.testTypeTable.columns,
            entityTerms: TEST_CONFIGS.ui.testTypeTable.entityTerms,
            tableOptions: TEST_CONFIGS.ui.testTypeTable.tableOptions,
            messageConfigMaps: MESSAGE_CONFIG_MAPS,
            entityFormatters: {
                testStatus: (value) => {
                    const statusLabels = GLOBAL_CONFIGS.ui.status;
                    return formatStatusDisplay(value, statusLabels);
                }
            },
            ...options
        });

        // Store user info for conditional columns
        this.userInfo = options.userInfo || null;
    }

    // Fetch test type data from API
    async fetchData() {
        try {
            const testTypes = await fetchTestTypesForLab(this.userInfo);
            return testTypes || [];
        } catch (error) {
            console.error('Failed to fetch test types:', error);
            throw error;
        }
    }

    // Override create method to pass user info for conditional columns
    async create(config = {}) {
        // Update userInfo if provided in config
        if (config.userInfo) {
            this.userInfo = config.userInfo;
        }

        // Set current config for conditional column evaluation
        this.currentConfig = {
            role: this.userInfo?.role || 'user',
            ...config
        };

        return super.create(config);
    }
}

// Factory function and default instance
export const createTestTypeTable = (options = {}) => new TestTypeTable(options);
export const testTypeTable = createTestTypeTable();
