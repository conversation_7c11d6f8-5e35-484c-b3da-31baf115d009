/*
 * Create Lab Test Page JavaScript
 * Handles test type creation with role-based access control and form validation
 */
import { TestApi } from '../../core/services/test-api.js';
import { TEST_CONFIGS } from '../../core/config/test-configs.js';
import { showMessage, clearMessage } from '../../core/helpers/message-helpers.js';
import { getLocalizedText } from '../../core/i18n/i18n-helpers.js';
import { getLabId } from './shared/lab-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';
import { redirectToPageByKey } from '../../core/helpers/navigation-helpers.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS,
    test: TEST_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    requiredRole: GLOBAL_CONFIGS.application.roles.LAB_ADMIN,
    onAuthSuccess: initializeCreateTestPage,
    onAuthFailure: showAccessDeniedError
};

// Initialize create test page
async function initializeCreateTestPage(userInfo) {
    try {
        await initializeTestCreationForm(userInfo);
    } catch (error) {
        console.error('Create test page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Initialize test creation form
async function initializeTestCreationForm(userInfo) {
    // Store user info for later use in duplicate checking
    window.currentUserInfo = userInfo;

    // Initialize event listeners
    initializeEventListeners();

    console.log('Test creation page initialized successfully');
    return true;
}

// Show access denied error for test creation
function showAccessDeniedError() {
    showMessage('#page-error-container', 'auth.messages.errors', 'accessDenied', MESSAGE_CONFIG_MAPS);

    // Redirect to home page after showing error with 3s delay
    setTimeout(() => {
        redirectToPageByKey('home');
    }, GLOBAL_CONFIGS.navigation.redirectDelay);
}

// Initialize event listeners for form interactions
function initializeEventListeners() {
    // Form submission
    const $form = $('#create-test-form');
    if ($form.length) {
        $form.on('submit', handleFormSubmit);
    }

    // Cancel button functionality
    const $cancelButton = $('#cancel-button');
    if ($cancelButton.length) {
        $cancelButton.on('click', handleCancel);
    }

    // Initialize form state management
    initializeFormStateManagement();
}

// Initialize form state management for both save and cancel buttons
function initializeFormStateManagement() {
    const $saveButton = $('#save-button');
    const $cancelButton = $('#cancel-button');
    const $testNameField = $('#test-name');
    const $testDescriptionField = $('#test-description');
    const $testStatusField = $('#test-status');

    if (!$saveButton.length || !$cancelButton.length || !$testNameField.length || !$testDescriptionField.length || !$testStatusField.length) {
        return;
    }

    $saveButton.prop('disabled', false);
    $cancelButton.prop('disabled', false);
}

// Validate test form data
function validateTestData(formData) {
    // Check required fields
    if (!formData.name || formData.name.trim().length === 0) {
        const fieldTerm = getLocalizedText({ message: TEST_CONFIGS.ui.fieldTerms.name }, 'message');
        showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
            field: fieldTerm
        });
        return false;
    }

    if (!formData.description || formData.description.trim().length === 0) {
        const fieldTerm = getLocalizedText({ message: TEST_CONFIGS.ui.fieldTerms.description }, 'message');
        showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
            field: fieldTerm
        });
        return false;
    }

    // Check field lengths using global standards
    if (formData.name && formData.name.length > GLOBAL_CONFIGS.form.standardLengths.shortName) {
        const fieldTerm = getLocalizedText({ message: TEST_CONFIGS.ui.fieldTerms.name }, 'message');
        showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldTooLong', MESSAGE_CONFIG_MAPS, {
            field: fieldTerm,
            maxLength: GLOBAL_CONFIGS.form.standardLengths.shortName
        });
        return false;
    }

    if (formData.description && formData.description.length > GLOBAL_CONFIGS.form.standardLengths.description) {
        const fieldTerm = getLocalizedText({ message: TEST_CONFIGS.ui.fieldTerms.description }, 'message');
        showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldTooLong', MESSAGE_CONFIG_MAPS, {
            field: fieldTerm,
            maxLength: GLOBAL_CONFIGS.form.standardLengths.description
        });
        return false;
    }

    return true;
}

// Check if test name already exists in current lab
async function checkTestNameDuplicate(testName) {
    try {
        // Get current user's lab ID for lab-specific duplicate checking
        const labId = getLabId(window.currentUserInfo);
        if (!labId) {
            console.warn('No lab ID available for duplicate checking');
            return false; // Continue with submission if lab ID not available
        }

        // Check for duplicates only within the current lab
        const existingTests = await new Promise((resolve, reject) => {
            TestApi.listTestTypes({
                name: testName,
                lab_id: labId
            })
                .done(resolve)
                .fail(reject);
        });
        return existingTests && existingTests.length > 0;
    } catch (error) {
        console.warn('Could not check for duplicate test names:', error);
        return false; // Continue with submission if check fails
    }
}


// Collect form data from DOM
function collectFormData() {
    const testName = $('#test-name').val().trim();
    const testDescription = $('#test-description').val().trim();
    const isActive = $('#test-status').prop('checked');

    return {
        name: testName,
        description: testDescription,
        is_active: isActive
    };
}

// Hide all message containers
function hideAllMessages() {
    const containers = ['#page-error-container', '#page-warning-container', '#page-success-container'];
    containers.forEach(selector => {
        clearMessage(selector);
    });
}


// Create test type with full business logic
async function createTestType(formData) {
    try {
        // Validate form data
        const isValid = validateTestData(formData);
        if (!isValid) {
            return { success: false, validationError: true };
        }

        // Check for duplicate test name
        const isDuplicate = await checkTestNameDuplicate(formData.name);
        if (isDuplicate) {
            const entityTerm = getLocalizedText({ message: TEST_CONFIGS.ui.table.entityTerms }, 'message');
            showMessage('#page-warning-container', 'global.messages.warnings.templates', 'duplicateName', MESSAGE_CONFIG_MAPS, {
                entity: entityTerm
            });
            return { success: false, isDuplicate: true };
        }

        // Submit to API with error handling
        await new Promise((resolve, reject) => {
            TestApi.createTestType(formData)
                .done(resolve)
                .fail(reject);
        });

        // Show success message
        const entityTerm = getLocalizedText({ message: TEST_CONFIGS.ui.table.entityTerms }, 'message');
        showMessage('#page-success-container', 'global.messages.success.templates', 'entityCreated', MESSAGE_CONFIG_MAPS, {
            entity: entityTerm
        });

        return { success: true };

    } catch (error) {
        console.error('Error creating test type:', error);

        // Show error message
        const entityTerm = getLocalizedText({ message: TEST_CONFIGS.ui.table.entityTerms }, 'message');
        showMessage('#page-error-container', 'global.messages.errors.templates', 'entityCreateFailed', MESSAGE_CONFIG_MAPS, {
            entity: entityTerm
        });

        return { success: false, error: error };
    }
}

// Handle complete form submission process
async function handleFormSubmission() {
    // Clear previous messages
    hideAllMessages();

    // Collect form data
    const formData = collectFormData();

    // Check if form is empty
    if (!formData.name || formData.name.trim().length === 0) {
        const fieldTerm = getLocalizedText({ message: TEST_CONFIGS.ui.fieldTerms.name }, 'message');
        showMessage('#page-error-container', 'global.messages.errors.templates', 'fieldRequired', MESSAGE_CONFIG_MAPS, {
            field: fieldTerm
        });
        return { success: false, emptyForm: true };
    }

    // Use business logic for test creation
    const result = await createTestType(formData);

    if (result.success) {
        // Redirect to manage test types page after a short delay
        redirectToPageByKey('manageTestTypes');
        return { success: true };
    } else if (result.isDuplicate) {
        // Duplicate name warning already shown by helper
        return { success: false, isDuplicate: true };
    } else if (result.validationError) {
        // Validation error already shown by helper
        return { success: false, validationError: true };
    }

    return { success: false };
}

// Handle form submission with loading state management
async function handleFormSubmit(event) {
    event.preventDefault();

    // Let wet-boew handle form validation
    const $form = $('#create-test-form');
    if (!$form[0].checkValidity()) {
        return;
    }

    // Simple button disable to prevent double-clicks
    const $submitButton = $('#save-button');
    $submitButton.prop('disabled', true);

    try {
        // Handle complete form submission process
        const result = await handleFormSubmission();

        // Re-enable button if submission failed
        if (!result.success) {
            $submitButton.prop('disabled', false);
        }
        // Button stays disabled on success since user will be redirected

    } catch (error) {
        console.error('Error in form submission:', error);
        // Re-enable button on error
        $submitButton.prop('disabled', false);
    }
}

// Handle cancel button click
function handleCancel(event) {
    if (event) event.preventDefault();

    hideAllMessages();

    showMessage('#page-success-container', 'global.messages.info.common', 'cancelled', MESSAGE_CONFIG_MAPS);

    // Redirect after 3 seconds for cancelled operations
    redirectToPageByKey('manageTestTypes'); 
}
