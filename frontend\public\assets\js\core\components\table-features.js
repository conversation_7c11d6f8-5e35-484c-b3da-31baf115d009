/*
 * Table Feature Mixins
 * Composable functionality for tables
 */

// Queue system for concurrent table creation (prevents WET-BOEW conflicts)
export const withQueue = (TableClass) => {
    return class QueuedTable extends TableClass {
        constructor(config) {
            super(config);
            this.initializationQueue = [];
            this.isInitializing = false;
        }

        // Override create method to use queue with temporary instances
        async create(config = {}) {
            return new Promise((resolve, reject) => {
                this.initializationQueue.push({ config, resolve, reject });
                this.processQueue();
            });
        }

        // Process queue sequentially using temporary instances
        async processQueue() {
            if (this.isInitializing || this.initializationQueue.length === 0) {
                return;
            }

            this.isInitializing = true;
            const task = this.initializationQueue.shift();

            try {
                // Create a temporary instance for this specific table
                const tempInstance = new this.constructor({
                    containerId: task.config.containerId || this.containerId,
                    tableId: task.config.tableId || this.tableId,
                    columns: this.columns,
                    entityTerms: this.entityTerms,
                    tableOptions: this.tableOptions,
                    messageConfigMaps: this.messageConfigMaps,
                    entityFormatters: this.entityFormatters
                });

                // Copy the fetchData method from the original instance
                tempInstance.fetchData = this.fetchData.bind(tempInstance);

                // Use the temporary instance to create the table
                await TableClass.prototype.create.call(tempInstance, task.config);

                task.resolve();
            } catch (error) {
                console.error(`Failed to initialize table:`, error);
                task.reject(error);
            } finally {
                this.isInitializing = false;
                this.processQueue();
            }
        }
    };
};

// Bulk actions functionality for management tables
export const withBulkActions = (TableClass) => {
    return class BulkActionsTable extends TableClass {
        constructor(config) {
            super(config);
            this.hasChanges = false;
            this.bulkCheckboxColumn = config.bulkCheckboxColumn;
        }

        // Override getVisibleColumns to replace last column with checkbox column
        getVisibleColumns() {
            let columns = super.getVisibleColumns();
            
            // Replace the last column with checkbox column for bulk actions
            if (this.bulkCheckboxColumn) {
                columns = [...columns];
                columns[columns.length - 1] = this.bulkCheckboxColumn;
            }
            
            return columns;
        }

        // Override buildTable to add bulk actions HTML
        buildTable(data) {
            const tableHtml = super.buildTable(data);
            return tableHtml + this.buildBulkActionsHtml();
        }

        // Override create to initialize event handlers after table creation
        async create(config = {}) {
            await super.create(config);
            this.initializeEventHandlers();
        }

        // Change tracking functionality
        trackChanges(hasChanges) {
            this.hasChanges = hasChanges;
            this.toggleBulkActions();
        }

        // Default implementations - subclasses override as needed
        buildBulkActionsHtml() {
            return '';
        }

        toggleBulkActions() {
            // Override in subclasses for bulk action UI updates
        }

        initializeEventHandlers() {
            // Override in subclasses for event binding
        }
    };
};

// Future: Additional mixins can be added here as needed
