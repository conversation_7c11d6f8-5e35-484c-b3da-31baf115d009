/*
 * Table Helper Functions
 * Utility functions for table creation and configuration
 */
import { GLO<PERSON>L_CONFIGS } from '../config/global-configs.js';

/**
 * Create standardized table configuration
 * Eliminates boilerplate code in table constructors
 * 
 * @param {Object} entityConfig - Entity-specific configuration (e.g., STAFF_CONFIGS, TEST_CONFIGS)
 * @param {Object} options - Override options from constructor
 * @returns {Object} Complete table configuration object
 */
export function createTableConfig(entityConfig, options = {}) {
    // Create MESSAGE_CONFIG_MAPS if not provided
    const messageConfigMaps = options.messageConfigMaps || {
        global: GLOBAL_CONFIGS,
        [getEntityKey(entityConfig)]: entityConfig
    };

    // Get the table config from the entity config
    const tableConfig = getTableConfig(entityConfig);

    return {
        containerId: options.containerId || tableConfig.containerId,
        tableId: options.tableId || tableConfig.tableId,
        columns: tableConfig.columns,
        entityTerms: tableConfig.entityTerms,
        tableOptions: tableConfig.tableOptions,
        bulkCheckboxColumn: tableConfig.bulkCheckboxColumn,
        messageConfigMaps: messageConfigMaps,
        entityFormatters: options.entityFormatters || {},
        ...options
    };
}

/**
 * Get table config from entity config
 * @param {Object} entityConfig - Entity configuration object
 * @returns {Object} Table configuration object
 */
function getTableConfig(entityConfig) {
    // Try different possible table config locations
    if (entityConfig.ui?.staffTable) return entityConfig.ui.staffTable;
    if (entityConfig.ui?.testTypeTable) return entityConfig.ui.testTypeTable;
    if (entityConfig.ui?.requisitionTable) return entityConfig.ui.requisitionTable;

    // Fallback to generic table config
    return entityConfig.ui?.table || {};
}

/**
 * Get entity key from config object for MESSAGE_CONFIG_MAPS
 * @param {Object} entityConfig - Entity configuration object
 * @returns {string} Entity key (e.g., 'staff', 'test', 'requisition')
 */
function getEntityKey(entityConfig) {
    // Try to determine entity key from config structure
    if (entityConfig.ui?.staffTable) return 'staff';
    if (entityConfig.ui?.testTypeTable) return 'test';
    if (entityConfig.ui?.requisitionTable) return 'requisition';

    // Fallback to 'entity' if can't determine
    return 'entity';
}

/**
 * Create standardized MESSAGE_CONFIG_MAPS
 * @param {Object} entityConfig - Entity-specific configuration
 * @returns {Object} MESSAGE_CONFIG_MAPS object
 */
export function createMessageConfigMaps(entityConfig) {
    return {
        global: GLOBAL_CONFIGS,
        [getEntityKey(entityConfig)]: entityConfig
    };
}
