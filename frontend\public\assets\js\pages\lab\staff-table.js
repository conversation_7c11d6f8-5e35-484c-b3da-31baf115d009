/*
 * Staff Table
 * Clean, simple table for displaying laboratory staff information
 * Uses new modular Table architecture
 */
import { Table } from '../../core/components/table.js';
import { LabApi } from '../../core/services/lab-api.js';
import { STAFF_CONFIGS } from '../../core/config/staff-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    staff: STAFF_CONFIGS
};

class StaffTable extends Table {
    constructor(options = {}) {
        super({
            containerId: options.containerId || STAFF_CONFIGS.ui.staffTable.containerId,
            tableId: options.tableId || STAFF_CONFIGS.ui.staffTable.tableId,
            columns: STAFF_CONFIGS.ui.staffTable.columns,
            entityTerms: STAFF_CONFIGS.ui.staffTable.entityTerms,
            tableOptions: STAFF_CONFIGS.ui.staffTable.tableOptions,
            messageConfigMaps: MESSAGE_CONFIG_MAPS,
            ...options
        });
    }

    // Fetch staff data from API with error handling
    async fetchData() {
        try {
            const staffData = await new Promise((resolve, reject) => {
                LabApi.getLabStaff()
                    .done(resolve)
                    .fail(reject);
            });
            return staffData || [];
        } catch (error) {
            console.error('Failed to fetch staff data:', error);
            throw error;
        }
    }
}

// Factory function and default instance
export const createStaffTable = (options = {}) => new StaffTable(options);
export const staffTable = createStaffTable();
