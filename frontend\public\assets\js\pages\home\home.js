/*
 * Home Page JavaScript
 * Handles initialization of requisition tables based on user role
 */
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { showMessage } from '../../core/helpers/message-helpers.js';
import { createRequisitionTable } from '../requisition/requisition-table.js';
import { getLabId, hasLabAssignment } from '../lab/shared/lab-helpers.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';
import { AUTH_CONFIGS } from '../../core/config/auth-configs.js';

// Create config maps for message helper
const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    auth: AUTH_CONFIGS
};

// Configure page authentication requirements
// This will be processed by authentication.js middleware
window.PAGE_AUTH_CONFIG = {
    onAuthSuccess: initializeHomePage
};

// Initialize home page
async function initializeHomePage(userInfo) {
    try {
        await initializeRequisitionTables(userInfo);
    } catch (error) {
        console.error('Home page initialization failed:', error);
        showMessage('#page-error-container', 'global.messages.errors.server', 'connectionError', MESSAGE_CONFIG_MAPS);
    }
}

// Initialize requisition tables based on user role
async function initializeRequisitionTables(userInfo) {
    // Create table configurations
    const openRequisitionsConfig = {
        ...REQUISITION_CONFIGS.ui.requisitionTable.queues.open,
        role: userInfo.role,
        labId: getLabId(userInfo)
    };

    const closedRequisitionsConfig = {
        ...REQUISITION_CONFIGS.ui.requisitionTable.queues.closed,
        role: userInfo.role,
        labId: getLabId(userInfo)
    };

    // Use role-based logic to decide which tables to create
    if (userInfo.role === GLOBAL_CONFIGS.application.roles.SCIENTIST) {
        await initializeScientistTables(openRequisitionsConfig, closedRequisitionsConfig);
    } else if (userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_ADMIN || userInfo.role === GLOBAL_CONFIGS.application.roles.LAB_PERSONNEL) {
        await initializeLabStaffTables(userInfo, openRequisitionsConfig);
    }
}

// Initialize tables for scientist role
async function initializeScientistTables(openConfig, closedConfig) {
    const $openSection = $('#open-requisitions-section');
    const $closedSection = $('#closed-requisitions-section');

    if ($openSection.length && $closedSection.length) {
        // Show both sections
        $openSection.prop('hidden', false).attr('aria-hidden', 'false');
        $closedSection.prop('hidden', false).attr('aria-hidden', 'false');

        // Create separate instances to avoid configuration conflicts
        const openTable = createRequisitionTable();
        const closedTable = createRequisitionTable();

        // Create both tables - shared queue prevents WET-BOEW conflicts
        await Promise.all([
            openTable.create(openConfig),
            closedTable.create(closedConfig)
        ]);

        console.log('Both requisition tables initialized successfully');
    }
}

// Initialize tables for lab staff roles
async function initializeLabStaffTables(userInfo, openConfig) {
    const $openSection = $('#open-requisitions-section');

    if ($openSection.length) {
        if (getLabId(userInfo)) {
            // Show open section
            $openSection.prop('hidden', false).attr('aria-hidden', 'false');

            const openTable = createRequisitionTable();
            await openTable.create(openConfig);
        } else {
            // Handle missing lab error
            showMessage('#page-error-container', 'auth.messages.errors', 'missingLabAssignment', MESSAGE_CONFIG_MAPS);
        }
    }
}