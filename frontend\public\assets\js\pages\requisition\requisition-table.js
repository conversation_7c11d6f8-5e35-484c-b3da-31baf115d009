/*
 * Requisition Table
 * Clean table for displaying requisition information with queue management
 * Uses Table with Queue mixin to prevent WET-BOEW conflicts on multi-table pages
 */
import { Table } from '../../core/components/table.js';
import { withQueue } from '../../core/components/table-features.js';
import { RequisitionApi } from '../../core/services/requisition-api.js';
import { REQUISITION_CONFIGS } from '../../core/config/requisition-configs.js';
import { GLOBAL_CONFIGS } from '../../core/config/global-configs.js';

const MESSAGE_CONFIG_MAPS = {
    global: GLOBAL_CONFIGS,
    requisition: REQUISITION_CONFIGS
};

class RequisitionTable extends withQueue(Table) {
    constructor(options = {}) {
        super({
            // Note: containerId and tableId will be provided by page initialization
            containerId: options.containerId || 'requisition-table-container',
            tableId: options.tableId || 'requisition-table',
            columns: REQUISITION_CONFIGS.ui.requisitionTable.columns,
            entityTerms: REQUISITION_CONFIGS.ui.requisitionTable.entityTerms,
            tableOptions: REQUISITION_CONFIGS.ui.requisitionTable.tableOptions,
            messageConfigMaps: MESSAGE_CONFIG_MAPS,
            ...options
        });
    }

    // Fetch requisitions data from API
    async fetchData() {
        const config = this.currentConfig;
        const requisitions = await new Promise((resolve, reject) => {
            RequisitionApi.getRequisitions(config.apiStatusQuery, config.labId, config.role)
                .done(resolve)
                .fail(reject);
        });

        // Backend now provides data sorted by timestamp (newest first)
        return requisitions || [];
    }



}

// Factory function and default instance
export const createRequisitionTable = (options = {}) => new RequisitionTable(options);
export const requisitionTable = createRequisitionTable();
